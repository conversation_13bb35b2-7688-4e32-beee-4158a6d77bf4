import { getAuthToken } from "@/features/auth/lib/auth-actions";
import axios from "axios";
export const API_ROUTE = process.env.API_ROUTE;

export const axiosInstance = axios.create({
	baseURL: API_ROUTE,
});

axiosInstance.interceptors.request.use(
	async config => {
		try {
			const token = await getAuthToken();
			if (token && config.headers) {
				config.headers.Authorization = `Bearer ${token}`;
			}
			return config;
		} catch (error) {
			console.error("Error getting auth token:", error);
			return config;
		}
	},
	error => {
		return Promise.reject(error);
	}
);
