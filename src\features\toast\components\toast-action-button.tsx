"use client";
import { motion } from "framer-motion";
import React from "react";
import { ToastAction } from "../types/toast.type";
import { ToastStyle } from "./toast-styles";

interface ToastActionButtonProps {
	action: ToastAction;
	style: ToastStyle;
	index: number;
	id: string;
	onAction: () => void;
}

export const ToastActionButton: React.FC<ToastActionButtonProps> = ({ action, style, index, id, onAction }) => {
	const getVariantClasses = () => {
		switch (action.variant) {
			case "primary":
				return `bg-${style.accent} text-white`;
			case "secondary":
				return "bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white";
			case "text":
			default:
				return "text-gray-700 dark:text-gray-300 hover:underline";
		}
	};

	return (
		<motion.button
			key={`${id}-action-${index}`}
			whileHover={{ scale: 1.02 }}
			whileTap={{ scale: 0.98 }}
			className={`py-1 px-3 text-xs rounded transition-colors ${getVariantClasses()}`}
			onClick={() => {
				action.onClick();
				onAction();
			}}
		>
			{action.label}
		</motion.button>
	);
};
