"use client";
import { motion } from "framer-motion";
import { useSet<PERSON>tom } from "jotai";
import React, { useEffect, useRef, useState } from "react";
import { pauseToastTimerAtom, removeToastAtom, resumeToastTimer<PERSON>tom } from "../atoms/toast.atom";
import { ToastProps } from "../types/toast.type";
import { ToastActionButton } from "./toast-action-button";
import { ToastIcon } from "./toast-icon";
import { getToastStyles } from "./toast-styles";

const Toast: React.FC<ToastProps> = ({
	id,
	message,
	title,
	type,
	duration: propDuration = 5000,
	actions = [],
	priority = "normal",
	dismissable = true,
	pauseOnHover = true,
	customContent,
}) => {
	const duration = type === "loading" ? Infinity : propDuration;
	const removeToast = useSetAtom(removeToastAtom);
	const pauseTimer = useSetAtom(pauseToastTimerAtom);
	const resumeTimer = useSetAtom(resumeToastTimer<PERSON>tom);

	const [, setProgress] = useState(100);
	const [expanded] = useState(false);

	const remainingTimeRef = useRef(duration);
	const startTimeRef = useRef(Date.now());
	const isInfinite = duration === Infinity;
	const messageRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (isInfinite) return;

		const interval = 10;
		startTimeRef.current = Date.now();
		remainingTimeRef.current = duration;

		const progressTimer = setInterval(() => {
			const elapsed = Date.now() - startTimeRef.current;
			const remaining = Math.max(0, duration - elapsed);
			remainingTimeRef.current = remaining;
			setProgress((remaining / duration) * 100);
		}, interval);

		return () => clearInterval(progressTimer);
	}, [duration, id, isInfinite]);

	const handleMouseEnter = () => {
		if (pauseOnHover && !isInfinite) {
			pauseTimer(id);
		}
	};

	const handleMouseLeave = () => {
		if (pauseOnHover && !isInfinite) {
			resumeTimer({ id, duration: remainingTimeRef.current });
			startTimeRef.current = Date.now();
		}
	};

	const styles = getToastStyles(type);
	const typeText =
		{
			success: "Sucesso",
			error: "Erro",
			warning: "Atenção",
			loading: "Carregando",
			info: "Informação",
		}[type] || "Informação";

	const hasTitle = !!title;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.2 }}
			className="relative w-full max-w-sm overflow-hidden rounded-md shadow-sm bg-white dark:bg-gray-800 flex border border-gray-100 dark:border-gray-700"
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
		>
			<div className={`w-1 ${styles.accent}`} />

			<div className="flex-1 flex">
				<div className="px-3 py-3">
					<div className={`w-5 h-5 ${styles.icon}`}>
						<svg className="w-full h-full stroke-current" viewBox="0 0 24 24" fill="none">
							<ToastIcon type={type} />
						</svg>
					</div>
				</div>

				<div className="flex-1 py-3 pr-8">
					<div className="font-medium text-sm text-gray-800 dark:text-gray-100">{hasTitle ? title : typeText}</div>

					<div
						ref={messageRef}
						className={`text-xs text-gray-600 dark:text-gray-300 mt-1 transition-all duration-300 ${
							expanded ? "max-h-96" : "max-h-[60px]"
						} overflow-hidden`}
					>
						{Array.isArray(message) ? (
							<ul className="list-disc pl-4 space-y-1">
								{message.map((msg, index) => (
									<li key={index + msg}>{msg}</li>
								))}
							</ul>
						) : (
							message
						)}
						{customContent && <div className="mt-1">{customContent}</div>}
					</div>

					{actions.length > 0 && (
						<div className="flex flex-wrap gap-2 mt-2">
							{actions.map((action, index) => (
								<ToastActionButton
									key={index + action.label}
									action={action}
									style={styles}
									index={index}
									id={id}
									onAction={() => {
										if (!isInfinite) {
											removeToast(id);
										}
									}}
								/>
							))}
						</div>
					)}
				</div>

				{dismissable && (
					<motion.button
						whileHover={{ scale: 1.1 }}
						whileTap={{ scale: 0.9 }}
						type="button"
						className="absolute top-2 right-2 text-gray-400 opacity-70 hover:opacity-100"
						onClick={() => removeToast(id)}
					>
						<svg className="w-3 h-3" viewBox="0 0 20 20" fill="currentColor">
							<path d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" />
						</svg>
					</motion.button>
				)}
			</div>

			{priority === "high" && <div className={`absolute top-0 right-0 w-1 h-1 rounded-full ${styles.accent} m-1`} />}
		</motion.div>
	);
};

export default Toast;
