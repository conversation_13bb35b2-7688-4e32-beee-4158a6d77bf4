"use client";

import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { ReactNode } from "react";
import { useUserSync } from "../hooks/user/use-user-sync.hook";
import { LoadingContainer } from "@/layout/components/container/loading-container";

interface IUserProviderProps extends IReactChildrenType {
	fallback?: ReactNode;
}

export function UserProvider({ children, fallback }: Readonly<IUserProviderProps>) {
	const { isLoading } = useUserSync();
	if (isLoading && fallback) return <>{fallback}</>;
	return <>{children}</>;
}

export function UserProviderWithLoading({ children, fallback }: Readonly<IUserProviderProps>) {
	return <UserProvider fallback={fallback ?? <LoadingContainer />}>{children}</UserProvider>;
}
