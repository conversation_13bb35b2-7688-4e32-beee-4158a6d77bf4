import * as AuthActions from "../lib/auth-actions";

export class AuthCookieManager {
	static readonly setAuthTokens = AuthActions.setAuthTokens;
	public static readonly getAuthToken = AuthActions.getAuthToken;
	public static readonly getRefreshToken = AuthActions.getRefreshToken;
	public static readonly clearTokens = AuthActions.clearAuthTokens;

	static async getAllTokens() {
		const [accessToken, refreshToken] = await Promise.all([this.getAuthToken(), this.getRefreshToken()]);
		return { accessToken, refreshToken };
	}

	static async isAuthenticated() {
		return (await this.getAuthToken()) !== null;
	}
}
