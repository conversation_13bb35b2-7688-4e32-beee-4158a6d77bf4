// components/KeycloakLoader.tsx
"use client";

import { useEffect, useRef } from "react";
import keycloak from "@/config/keycloack/config";
import { createCookie } from "@/shared/lib/cookies/crud/create";
import { COOKIE_NAMES } from "@/config/cookies/name";
import { getCookie } from "@/shared/lib/cookies/crud/get";
import { useNavigatePaths } from "@/shared/hooks/utils";

export default function KeycloakLoader() {
	const { replaceToCurrent } = useNavigatePaths();

	useEffect(() => {
		keycloak
			.init({ onLoad: "login-required" })
			.then(async authenticated => {
				console.log("Authenticated:", authenticated);

				if (authenticated) {
					if (keycloak.token) {
						await createCookie({
							name: COOKIE_NAMES.ACCESS_TOKEN,
							value: keycloak.token,
						});
					}

					replaceToCurrent();
					console.log("Tokens armazenados com sucesso");
				} else {
					replaceToCurrent();
				}
			})
			.catch(err => {
				console.error("Keycloak init error", err);
			});
	}, []);

	return null;
}
