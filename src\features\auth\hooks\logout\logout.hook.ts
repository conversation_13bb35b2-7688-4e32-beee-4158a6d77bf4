import { useNavigatePaths } from "@/shared/hooks/utils";
import { useQueryClient } from "@tanstack/react-query";
import { AuthCookieManager } from "../../services/auth-cookie.service";
import { toast } from "@/features/toast";

export const useLogout = () => {
	const { replaceToCurrent } = useNavigatePaths();
	const queryClient = useQueryClient();

	const logout = async () => {
		await AuthCookieManager.clearTokens();
		queryClient.clear();
		replaceToCurrent();
	};

	return {
		logout: () => toast.promise(logout(), { loading: "Saindo..." }),
	};
};
