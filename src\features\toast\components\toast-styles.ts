export interface ToastStyle {
	accent: string;
	icon: string;
	progress: string;
}

export const getToastStyles = (type: "success" | "error" | "warning" | "loading" | "info"): ToastStyle => {
	const baseStyles: ToastStyle = {
		accent: "",
		icon: "",
		progress: "",
	};

	switch (type) {
		case "success":
			return {
				...baseStyles,
				accent: "bg-green-500",
				icon: "text-green-500",
				progress: "bg-green-500",
			};
		case "error":
			return {
				...baseStyles,
				accent: "bg-red-500",
				icon: "text-red-500",
				progress: "bg-red-500",
			};
		case "warning":
			return {
				...baseStyles,
				accent: "bg-amber-500",
				icon: "text-amber-500",
				progress: "bg-amber-500",
			};
		case "loading":
			return {
				...baseStyles,
				accent: "bg-blue-500",
				icon: "text-blue-500",
				progress: "bg-blue-500",
			};
		case "info":
		default:
			return {
				...baseStyles,
				accent: "bg-violet-500",
				icon: "text-violet-500",
				progress: "bg-violet-500",
			};
	}
};
