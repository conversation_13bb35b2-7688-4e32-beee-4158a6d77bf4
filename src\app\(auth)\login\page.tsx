import { LoginForm } from "@/features/auth/components/login-form";
import LogoMinAnimatedMobile from "@/layout/components/logo-mobile/logo-animated-mobile";
import LogoMinAnimated from "@/layout/components/logo/logo-animated";

const LoginPage = () => {
    return (
        <div className="flex justify-center items-center w-screen h-screen bg-gradient-to-r from-[#FFFFFF] via-[#8098AF] to-[#3A6891]">
            <div className="relative flex flex-col md:flex-row w-[22rem] sm:w-[32.06rem] md:w-[48.06rem] lg:w-[64.125rem] h-[24rem] md:h-[36.375rem] bg-[#F2F2F2] rounded-[0.625rem] shadow-lg shadow-black/15 overflow-hidden">
                <div className="hidden  md:flex md:w-1/2 items-center justify-center">
                    <div>
                        <LogoMinAnimated className="hidden md:block w-[20.68rem] h-[7.1rem] lg:w-[23.68rem] lg:h-[7.1rem]" />
                    </div>
                </div>

                <span className="hidden md:block absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-0.5 h-[70%] bg-[#00325F]"></span>

                <div className="w-full h-full md:w-1/2 flex flex-col gap-5 items-center justify-center p-6">
                    <LogoMinAnimatedMobile className=" md:hidden" />
                    <LoginForm />
                </div>
            </div>
        </div>
    );
};

export default LoginPage;
