import { IR<PERSON> } from "@/config/enums/role.enum";

import { getAuthToken, getRefreshToken } from "../lib/auth-actions";
import { IUser } from "../types/user.types";
import { getJWTPayloadProperty } from "@/shared/lib/jwt/get-property";
import { ApiResponse } from "@/shared/types/requests/request.type";

export class UserCookieManager {
	static async getUserFromCookie(): Promise<ApiResponse<IUser | null>> {
		const token = (await getAuthToken()) ?? (await getRefreshToken());
		if (!token) return { success: false, data: { message: "Token não encontrado" }, status: 401 };
		try {
			const id = getJWTPayloadProperty<string>(token, "id") ?? "";
			const name = getJWTPayloadProperty<string>(token, "name") ?? "";
			const permissions = getJWTPayloadProperty<IRole[]>(token, "roles") || [];
			return { success: true, data: { id, name, permissions }, status: 200 };
		} catch (error) {
			return {
				success: false,
				data: { message: error instanceof Error ? error.message : "Erro ao obter usuário do cookie" },
				status: 500,
			};
		}
	}

	static async isAuthenticated(): Promise<boolean> {
		const { success, data } = await this.getUserFromCookie();
		return !!(success && data);
	}
}
