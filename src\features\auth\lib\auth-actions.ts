"use server";

import { CO<PERSON>IE_NAMES } from "@/config/cookies/name";
import { createCookie } from "@/shared/lib/cookies/crud/create";
import { getCookie } from "@/shared/lib/cookies/crud/get";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { getJWTMaxAge } from "@/shared/lib/jwt/max-age";
import { ApiResponse } from "@/shared/types/requests/request.type";

export const clearAuthTokens = async (): Promise<ApiResponse<{ message: string }>> => {
	const results = await Promise.allSettled([removeCookie({ name: COOKIE_NAMES.ACCESS_TOKEN }), removeCookie({ name: COOKIE_NAMES.REFRESH_TOKEN })]);
	if (results.some(r => r.status === "rejected"))
		return {
			success: false,
			data: { message: "Erro ao remover tokens" },
			status: 500,
		};
	if (!results.some(r => r.status === "fulfilled" && r.value.success))
		return {
			success: false,
			data: { message: "Nenhum token foi encontrado para remover" },
			status: 404,
		};
	return {
		success: true,
		data: { message: "Tokens removidos com sucesso" },
		status: 200,
	};
};

export const getAuthToken = async (): Promise<string | null> => {
	const { success, value } = await getCookie({
		name: COOKIE_NAMES.ACCESS_TOKEN,
	});
	return success ? value : null;
};

export const getRefreshToken = async (): Promise<string | null> => {
	const { success, value } = await getCookie({
		name: COOKIE_NAMES.REFRESH_TOKEN,
	});
	return success ? value : null;
};

export const setAuthTokens = async (accessToken: string, refreshToken?: string): Promise<ApiResponse<boolean>> => {
	const setCookie = async (name: string, token: string) => {
		const maxAge = getJWTMaxAge(token) ?? 0;
		return createCookie({
			name,
			value: token,
			options: { secure: true, sameSite: "strict", maxAge },
		});
	};

	const accessRes = await setCookie(COOKIE_NAMES.ACCESS_TOKEN, accessToken);
	if (!accessRes.success) return accessRes;

	if (refreshToken) {
		const refreshRes = await setCookie(COOKIE_NAMES.REFRESH_TOKEN, refreshToken);
		if (!refreshRes.success) {
			await clearAuthTokens();
			return refreshRes;
		}
	}

	return { success: true, data: true, status: 200 };
};
