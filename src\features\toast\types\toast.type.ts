export type ToastType = "success" | "error" | "info" | "warning" | "loading";
export type ToastPosition = "top-right" | "top-left" | "bottom-right" | "bottom-left" | "top-center" | "bottom-center";

export type ToastPriority = "low" | "normal" | "high";

export interface ToastAction {
	label: string;
	onClick: () => void;
	variant?: "primary" | "secondary" | "text";
}

export interface ToastProps {
	id: string;
	message: string;
	title?: string;
	type: ToastType;
	duration?: number;
	actions?: ToastAction[];
	priority?: ToastPriority;
	dismissable?: boolean;
	pauseOnHover?: boolean;
	customContent?: React.ReactNode;
	origin?: string;
}

export interface ToastContainerProps {
	position?: ToastPosition;
	maxToasts?: number;
}

export interface ToastOptions {
	title?: string;
	duration?: number;
	actions?: ToastAction[];
	priority?: ToastPriority;
	dismissable?: boolean;
	pauseOnHover?: boolean;
	customContent?: React.ReactNode;
	onClose?: () => void;
	onAction?: (actionId: string) => void;
	icon?: React.ReactNode;
}

export interface ToastConfiguration {
	position?: ToastPosition;
	maxToasts?: number;
	defaultDuration?: number;
	pauseOnWindowBlur?: boolean;
	defaultDismissable?: boolean;
}

export interface IToastStore {
	addToast(message: string, type: ToastType, options?: ToastOptions): string;
	removeToast(id: string): void;
	pauseToastTimer(id: string): void;
	resumeToastTimer(id: string, remainingTime: number): void;
	updateToastConfig(config: Partial<ToastConfiguration>): void;
}

export interface IToastPresenter {
	success(message: string, options?: ToastOptions): string;
	error(message: string | string[], options?: ToastOptions): string;
	warning(message: string, options?: ToastOptions): string;
	info(message: string, options?: ToastOptions): string;
	loading(message: string, options?: ToastOptions): string;
	custom(customContent: React.ReactNode, type?: ToastType, options?: ToastOptions): string;
	promise<T>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success?: string;
			error?: string;
		},
		options?: ToastOptions
	): Promise<T>;
}

export interface IToastService extends IToastPresenter {
	dismiss(id: string): void;
	pauseTimer(id: string): void;
	resumeTimer(id: string, remainingTime: number): void;
	// update(id: string, message: string, options?: Partial<ToastOptions>): void;
	configure(config: Partial<ToastConfiguration>): void;
	// clearAll(types?: ToastType[]): void;
}
