@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
	/* ===== CORES PRINCIPAIS ===== */
	--primary: #004475;
	--primary-foreground: #ffffff;
	--secondary: #f1f5f9;
	--secondary-foreground: #1e293b;

	/* ===== CORES DE FUNDO ===== */
	--background: #f9f9fa;
	--background-secondary: #eceef2;
	--foreground: #000000;

	/* ===== CORES DE TEXTO ===== */
	--text-primary: #000000;
	--text-secondary: #514d4d;

	/* ===== CORES DE BORDA E INPUT ===== */
	--border: #e0e0e0;
	--input: #e0e0e0;
	--ring: #004475;

	/* ===== CORES DE ACENTO ===== */
	--accent: #f1f5f9;
	--accent-foreground: #1e293b;
	--muted: #f1f5f9;
	--muted-foreground: #64748b;

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--destructive: #ef4444;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS ===== */
	--leaf-green-color: #00a03c;

	/* ===== CORES DE CARD (MANTIDAS COMO ESTÃO) ===== */
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.129 0.042 264.695);

	/* ===== CORES DE POPOVER ===== */
	--popover: #ffffff;
	--popover-foreground: #000000;

	/* ===== CORES DE CHART ===== */
	--chart-1: #3b82f6;
	--chart-2: #10b981;
	--chart-3: #f59e0b;
	--chart-4: #ef4444;
	--chart-5: #8b5cf6;

	/* ===== CORES DE SIDEBAR ===== */
	--sidebar: #ffffff;
	--sidebar-foreground: #000000;
	--sidebar-primary: #004475;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #f1f5f9;
	--sidebar-accent-foreground: #1e293b;
	--sidebar-border: #e0e0e0;
	--sidebar-ring: #004475;

	/* ===== RADIUS ===== */
	--radius: 0.938rem;
	--radius-controls: 0.625rem;
}

@layer base {
	[data-theme="blue-white"] {
		--color-primary: #004475;
		--color-background: #ffffff;
		--color-leaf-green-color: #00a03c;
		--color-sidebar-group-title: #777790;
	}

	[data-theme="blue-dark"] {
		--color-primary: #004475;
		--color-background: #000000;
		--color-leaf-green-color: #00a03c;
	}
}

@theme inline {
	/* ===== CORES PRINCIPAIS ===== */
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);

	/* ===== CORES DE FUNDO ===== */
	--color-background: var(--background);
	--color-background-secondary: var(--background-secondary);
	--color-foreground: var(--foreground);

	/* ===== CORES DE TEXTO ===== */
	--color-text-primary: var(--text-primary);
	--color-text-secondary: var(--text-secondary);

	/* ===== CORES DE BORDA E INPUT ===== */
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);

	/* ===== CORES DE ACENTO ===== */
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);

	/* ===== CORES ESPECÍFICAS ===== */
	--color-leaf-green-color: var(--leaf-green-color);

	/* ===== CORES DE CARD ===== */
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);

	/* ===== CORES DE POPOVER ===== */
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);

	/* ===== CORES DE CHART ===== */
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);

	/* ===== CORES DE SIDEBAR ===== */
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	/* ===== RADIUS ===== */
	--radius-main: var(--radius);
	--radius-controls: var(--radius-controls);
}

.dark {
	/* ===== CORES PRINCIPAIS ===== */
	--primary: #004475;
	--primary-foreground: #ffffff;
	--secondary: #1e293b;
	--secondary-foreground: #f1f5f9;

	/* ===== CORES DE FUNDO ===== */
	--background: #000000;
	--background-secondary: #0f172a;
	--foreground: #ffffff;

	/* ===== CORES DE TEXTO ===== */
	--text-primary: #ffffff;
	--text-secondary: #cbd5e1;

	/* ===== CORES DE BORDA E INPUT ===== */
	--border: #334155;
	--input: #334155;
	--ring: #004475;

	/* ===== CORES DE ACENTO ===== */
	--accent: #1e293b;
	--accent-foreground: #f1f5f9;
	--muted: #1e293b;
	--muted-foreground: #94a3b8;

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--destructive: #ef4444;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS ===== */
	--leaf-green-color: #00a03c;

	/* ===== CORES DE CARD (MANTIDAS COMO ESTÃO) ===== */
	--card: oklch(0.208 0.042 265.755);
	--card-foreground: oklch(0.984 0.003 247.858);

	/* ===== CORES DE POPOVER ===== */
	--popover: #0f172a;
	--popover-foreground: #ffffff;

	/* ===== CORES DE CHART ===== */
	--chart-1: #3b82f6;
	--chart-2: #10b981;
	--chart-3: #f59e0b;
	--chart-4: #ef4444;
	--chart-5: #8b5cf6;

	/* ===== CORES DE SIDEBAR ===== */
	--sidebar: #0f172a;
	--sidebar-foreground: #ffffff;
	--sidebar-primary: #004475;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #1e293b;
	--sidebar-accent-foreground: #f1f5f9;
	--sidebar-border: #334155;
	--sidebar-ring: #004475;
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
	}
}
