"use client";
import { AnimatePresence, motion } from "framer-motion";
import { use<PERSON>tom, useAtomValue } from "jotai";
import { useEffect } from "react";

import { maxToastsAtom, toastPosition<PERSON>tom, toasts<PERSON>tom } from "../atoms/toast.atom";
import { ToastContainerProps, ToastPosition } from "../types/toast.type";
import Toast from "./toast";

const getPositionStyles = (position: ToastPosition): string => {
	switch (position) {
		case "top-left":
			return "top-4 left-4 items-start";
		case "top-center":
			return "top-4 left-1/2 -translate-x-1/2 items-center";
		case "top-right":
			return "top-4 right-4 items-end";
		case "bottom-left":
			return "bottom-4 left-4 items-start";
		case "bottom-center":
			return "bottom-4 left-1/2 -translate-x-1/2 items-center";
		case "bottom-right":
			return "bottom-4 right-4 items-end";
		default:
			return "top-4 right-4 items-end";
	}
};

const getAnimationByPosition = (position: ToastPosition) => {
	const isBottom = position.includes("bottom");
	const isLeft = position.includes("left");
	const isRight = position.includes("right");

	const baseAnimation = {
		animate: { opacity: 1, scale: 1, x: 0, y: 0 },
	};

	const yOffset = isBottom ? 20 : -20;
	const yExitOffset = isBottom ? 10 : -10;

	let xOffset = 0;
	if (isLeft) {
		xOffset = -20;
	} else if (isRight) {
		xOffset = 20;
	}

	return {
		initial: { opacity: 0, scale: 0.8, y: yOffset, x: xOffset },
		animate: baseAnimation.animate,
		exit: { opacity: 0, scale: 0.8, y: yExitOffset, x: xOffset / 2 },
	};
};

const ToastContainer: React.FC<ToastContainerProps> = props => {
	const [toasts, setToasts] = useAtom(toastsAtom);
	const position = useAtomValue(toastPositionAtom);
	const maxToasts = useAtomValue(maxToastsAtom);

	const finalPosition = props.position ?? position;
	const finalMaxToasts = props.maxToasts ?? maxToasts;

	useEffect(() => {
		if (toasts.length > 1) {
			const loadingToasts = toasts.filter(t => t.type === "loading");
			const nonLoadingToasts = toasts.filter(t => t.type !== "loading");

			if (loadingToasts.length > 0 && nonLoadingToasts.length > 0) {
				setToasts([nonLoadingToasts[nonLoadingToasts.length - 1]]);
			} else {
				setToasts([toasts[toasts.length - 1]]);
			}
		}
	}, [toasts, setToasts]);

	const positionClasses = getPositionStyles(finalPosition);

	const priorityOrder = { high: 0, normal: 1, low: 2 };
	const sortedToasts = [...toasts]
		.sort((a, b) => {
			const priorityA = priorityOrder[a.priority ?? "normal"];
			const priorityB = priorityOrder[b.priority ?? "normal"];
			return priorityA - priorityB;
		})
		.slice(0, finalMaxToasts);

	const toastDirection = finalPosition.includes("bottom") ? "flex-col-reverse" : "flex-col";

	return (
		<div className={`fixed ${positionClasses} z-50 flex ${toastDirection} gap-2 pointer-events-none`}>
			<AnimatePresence mode="popLayout">
				{sortedToasts.map(toast => {
					const animation = getAnimationByPosition(finalPosition);

					return (
						<motion.div
							key={toast.id}
							initial={animation.initial}
							animate={animation.animate}
							exit={animation.exit}
							transition={{
								type: "spring",
								duration: 0.4,
								bounce: 0.3,
								stiffness: 200,
								damping: 15,
							}}
							className="pointer-events-auto w-full max-w-sm"
						>
							<Toast {...toast} />
						</motion.div>
					);
				})}
			</AnimatePresence>
		</div>
	);
};

export default ToastContainer;
