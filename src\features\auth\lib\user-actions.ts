"use server";

import { ApiResponse } from "@/shared/types/requests/request.type";
import { IUser } from "../types/user.types";
import { createGetRequest } from "@/shared/lib/requests";
import { IRole } from "@/config/enums/role.enum";
import { AUTH_ENDPOINTS } from "../api/endpoints";

type IGetCurrentUserResponse = {
	id: number;
	name: string;
	document: string;
	roles: IRole[];
};

export const getCurrentUser = async (): Promise<ApiResponse<IUser>> => {
	const res = await createGetRequest<IGetCurrentUserResponse>(AUTH_ENDPOINTS.PROFILE);
	if (!res.success) return res;
	const { id, name, roles } = res.data;
	return {
		success: true,
		status: 200,
		data: {
			id: id?.toString() || "",
			name: name || "",
			permissions: roles || [],
		},
	};
};
