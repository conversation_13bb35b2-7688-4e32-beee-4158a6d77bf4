import { AccessTokenValidator } from "@/config/services/access-token-validator";
import { CascadeTokenValidator } from "@/config/services/cascade-token-validator";
import { RefreshTokenValidator } from "@/config/services/refresh-token-validator";
import { NextRequest, NextResponse } from "next/server";

export async function authMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname } = request.nextUrl;
	const accessTokenValidator = new AccessTokenValidator();
	const refreshTokenValidator = new RefreshTokenValidator();
	const cascadeValidator = new CascadeTokenValidator([accessTokenValidator, refreshTokenValidator]);
	const validationResult = await cascadeValidator.validate(request);

	if (!validationResult.isValid || validationResult.status !== "ok") {
		const loginUrl = new URL("/login", request.url);
		loginUrl.searchParams.set("redirect", pathname);
		return NextResponse.rewrite(loginUrl);
	}

	return NextResponse.next();
}
