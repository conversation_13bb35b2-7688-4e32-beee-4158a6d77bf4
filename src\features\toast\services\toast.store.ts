import { getDefaultStore } from "jotai";
import {
	addToast<PERSON>tom,
	// clearAllToasts<PERSON>tom,
	pauseToastTimer<PERSON>tom,
	removeToast<PERSON>tom,
	resumeToastTimer<PERSON>tom,
	// updateToast<PERSON>tom,
	updateToastConfigAtom,
} from "../atoms/toast.atom";

import { IToastStore, ToastConfiguration, ToastOptions, ToastType } from "../types/toast.type";
import { SIMP_STORE } from "@/config/providers/global";

export class JotaiToastStore implements IToastStore {
	private getStore() {
		if (typeof window !== "undefined" && SIMP_STORE) return SIMP_STORE;
		return getDefaultStore();
	}

	addToast(message: string, type: ToastType, options?: ToastOptions): string {
		const store = this.getStore();
		if (!store) {
			console.error("Nenhuma store disponível para mostrar o toast");
			return "";
		}

		return store.set(addToastAtom, {
			message,
			type,
			...options,
		});
	}

	removeToast(id: string): void {
		this.getStore().set(removeToast<PERSON>tom, id);
	}

	pauseToastTimer(id: string): void {
		this.getStore().set(pauseToastTimer<PERSON>tom, id);
	}

	resumeToastTimer(id: string, remainingTime: number): void {
		this.getStore().set(resumeToastTimerAtom, { id, duration: remainingTime });
	}

	updateToastConfig(config: Partial<ToastConfiguration>): void {
		this.getStore().set(updateToastConfigAtom, config);
	}

	// updateToast(id: string, message: string, options?: Partial<ToastOptions>): void {
	// 	this.getStore().set(updateToastAtom, { id, message, ...options });
	// }

	// clearAllToasts(types?: ToastType[]): void {
	// 	this.getStore().set(clearAllToastsAtom, types);
	// }
}
