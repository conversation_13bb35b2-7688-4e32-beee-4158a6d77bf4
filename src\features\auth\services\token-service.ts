// import { AxiosInstance, AxiosResponse, AxiosRequestConfig as OriginalAxiosRequestConfig } from "axios";
// import { AUTH_ENDPOINTS } from "../api/endpoints";

// import { clearAuthTokens, getRefreshToken } from "../lib/auth-actions";

// interface AxiosRequestConfig extends OriginalAxiosRequestConfig {
// 	_retry?: boolean;
// }

// export const REFRESH_ERROR_MESSAGE = "O token de atualização expirou. Por favor, faça login novamente.";

// class TokenService {
// 	private isRefreshing = false;
// 	private refreshSubscribers: Array<(token: string) => void> = [];
// 	private readonly nonRefreshableEndpoints = [
// 		AUTH_ENDPOINTS.LOGIN,
// 		// AUTH_ENDPOINTS.REFRESH
// 	];

// 	// constructor(private readonly axiosInstance: AxiosInstance) {
// 	// 	this.setupRefreshInterceptor();
// 	// }

// 	private isNonRefreshableEndpoint(url: string): boolean {
// 		return this.nonRefreshableEndpoints.some(endpoint => url.includes(endpoint));
// 	}

// 	private resetRefreshState(): void {
// 		this.isRefreshing = false;
// 		this.refreshSubscribers = [];
// 	}

// 	private notifySubscribers(token: string): void {
// 		this.refreshSubscribers.forEach(cb => cb(token));
// 		this.resetRefreshState();
// 	}

// 	// private async handleTokenRefresh(originalRequest: AxiosRequestConfig): Promise<AxiosResponse> {
// 	// 	if (this.isRefreshing) {
// 	// 		return new Promise(resolve => {
// 	// 			this.refreshSubscribers.push(token => {
// 	// 				originalRequest.headers = { ...originalRequest.headers, Authorization: `Bearer ${token}` };
// 	// 				resolve(this.axiosInstance(originalRequest));
// 	// 			});
// 	// 		});
// 	// 	}

// 	// 	this.isRefreshing = true;
// 	// 	originalRequest._retry = true;

// 	// 	try {
// 	// 		const refreshToken = await getRefreshToken();
// 	// 		if (!refreshToken) {
// 	// 			await clearAuthTokens();
// 	// 			this.resetRefreshState();
// 	// 			return Promise.reject(new Error("Refresh token não encontrado."));
// 	// 		}
// 	// 		const refreshResult = await refreshRequest({ token: refreshToken });

// 	// 		if (!refreshResult.success) {
// 	// 			await clearAuthTokens();
// 	// 			this.notifySubscribers(REFRESH_ERROR_MESSAGE);
// 	// 			throw new Error(REFRESH_ERROR_MESSAGE);
// 	// 		}

// 	// 		const { access_token } = refreshResult.data;
// 	// 		this.notifySubscribers(access_token);
// 	// 		originalRequest.headers = { ...originalRequest.headers, Authorization: `Bearer ${access_token}` };
// 	// 		return this.axiosInstance(originalRequest);
// 	// 	} catch (error) {
// 	// 		await clearAuthTokens();
// 	// 		this.resetRefreshState();
// 	// 		if (error instanceof Error) {
// 	// 			throw error;
// 	// 		}
// 	// 		throw new Error(String(error));
// 	// 	}
// 	// }

// 	// private setupRefreshInterceptor(): void {
// 	// 	this.axiosInstance.interceptors.response.use(
// 	// 		response => response,
// 	// 		async error => {
// 	// 			const originalRequest = error.config;
// 	// 			if (error.response?.status === 401 && !originalRequest._retry && !this.isNonRefreshableEndpoint(originalRequest.url)) {
// 	// 				try {
// 	// 					return await this.handleTokenRefresh(originalRequest);
// 	// 				} catch (refreshError) {
// 	// 					if (refreshError instanceof Error) {
// 	// 						return Promise.reject(refreshError);
// 	// 					}
// 	// 					return Promise.reject(new Error(String(refreshError)));
// 	// 				}
// 	// 			}
// 	// 			return Promise.reject(error instanceof Error ? error : new Error(String(error)));
// 	// 		}
// 	// 	);
// 	// }
// }

// export default TokenService;
