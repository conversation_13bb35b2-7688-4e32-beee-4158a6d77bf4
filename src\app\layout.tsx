import type { <PERSON>ada<PERSON> } from "next";
import { Montser<PERSON> } from "next/font/google";
import "../layout/styles/index.css";
import { ProviderGlobal } from "@/config/providers/global";
import ErrorWrapper from "./error-wapper";
import KeycloakLoader from "@/features/auth/components/keyclock-loader";

const montserrat = Montserrat({ subsets: ["latin"] });

export const metadata: Metadata = {
	title: "S.I.M³P",
	description: "Sistema Integrado de Medição e Manufatura Pormade",
	icons: { icon: "/favicon.svg" },
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="pt-BR">
			<body className={`${montserrat.className}  bg-white antialiased`}>
				<ErrorWrapper>
					<ProviderGlobal>{children}</ProviderGlobal>
				</ErrorWrapper>
			</body>
		</html>
	);
}
