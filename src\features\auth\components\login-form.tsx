"use client";

import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { useLoginForm } from "../hooks/login/login-form.hook";
import { useLoginMutation } from "../hooks/login/login-mutation.hook";
import { Input } from "@/shared/components/shadcn/input";
import { Button } from "@/shared/components/shadcn/button";

export const LoginForm = () => {
    const { login, isLoading } = useLoginMutation();
    const methods = useLoginForm();

    return (
        <Form {...methods}>
            <form onSubmit={methods.handleSubmit(login)} className="flex flex-col items-center gap-6 w-full px-4">
                <FormField
                    control={methods.control}
                    name="username"
                    render={({ field }) => (
                        <FormItem className="w-full max-w-[21.62rem]">
                            <FormLabel>Usuário</FormLabel>
                            <FormControl>
                                <Input
                                    placeholder="Usuário"
                                    {...field}
                                    className="bg-primary/15 border border-primary h-[2.87rem] focus:outline-none"
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <FormField
                    control={methods.control}
                    name="password"
                    render={({ field }) => (
                        <FormItem className="w-full max-w-[21.62rem]">
                            <FormLabel>Senha</FormLabel>
                            <FormControl>
                                <Input
                                    type="password"
                                    placeholder="Senha"
                                    {...field}
                                    className="bg-primary/15 border border-primary rounded-controls h-[2.87rem] focus:outline-none"
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <Button type="submit" className="self-center h-[2.87rem]   cursor-pointer w-full max-w-[16.75rem] mt-4.5 rounded-control">
                    {isLoading ? "Carregando..." : "Entrar"}
                </Button>
            </form>
        </Form>
    );
};
