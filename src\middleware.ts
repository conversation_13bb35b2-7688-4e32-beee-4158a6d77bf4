import { NextRequest, NextResponse } from "next/server";
import { authMiddleware } from "./config/middleware/auth/auth.middleware";
import { permissionsMiddleware } from "./config/middleware/auth/route.middleware";

export async function middleware(request: NextRequest): Promise<Response> {
	// const authResponse = await authMiddleware(request);
	// if (authResponse.headers.get("x-middleware-rewrite") || authResponse.headers.get("x-middleware-redirect")) return authResponse;
	// const permissionsResponse = permissionsMiddleware(request);
	// if (permissionsResponse.headers.get("x-middleware-rewrite") || permissionsResponse.headers.get("x-middleware-redirect"))
	// 	return permissionsResponse;

	return NextResponse.next();
}

export const config = {
	matcher: ["/((?!_next/static|_next/image|_next/font|favicon.ico|api|login|events/devtools/events|events$|\\.well-known).*)"],
};
