"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSet<PERSON>tom } from "jotai";
import { useEffect } from "react";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom } from "../../atoms/user.atom";
import { getCurrentUser } from "../../lib/user-actions";

export function useUserSync() {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();

	const { data, isLoading, error } = useQuery({
		queryKey: ["user"],
		queryFn: getCurrentUser,
	});

	useEffect(() => {
		setIsAuthenticated(!!data?.success && !!data?.data);
		setUser(data?.success && data?.data ? data.data : null);
	}, [data, setUser, setIsAuthenticated]);

	const refreshUser = () => queryClient.invalidateQueries({ queryKey: ["user"] });

	return {
		user: data?.data ?? null,
		isLoading,
		error,
		isAuthenticated: !!data?.data,
		refreshUser,
	};
}
